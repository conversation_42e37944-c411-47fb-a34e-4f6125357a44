<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Alert System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #e1e5e9;
        }
        .test-result {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .test-error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
    </style>
</head>
<body>
    <h1>Alert System Test</h1>
    
    <div class="test-section">
        <h2>Test Alert Generation</h2>
        <p>This test creates sample transaction data and verifies that the alert system correctly identifies two-day high-value debit/credit patterns with matching counter parties and minimum amount threshold of 300,000.</p>
        <button onclick="runAlertTest()">Run Alert Test</button>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>Test Data</h2>
        <p>Sample transactions that should generate consolidated two-day high-value alerts:</p>
        <ul>
            <li>CUST001: $500,000 Dr (2024-01-15) and $500,000 Cr (2024-01-16) with same counter party "ABC Corp" (1 pair - QUALIFIES)</li>
            <li>CUST002: $100,000 Dr (2024-01-16) and $100,000 Cr (2024-01-17) with same counter party "DEF Inc" (1 pair - BELOW THRESHOLD)</li>
            <li>CUST003: $750,000 Dr (2024-01-17) and $750,000 Cr (2024-01-17) with same counter party "GHI Corp" (1 pair - QUALIFIES)</li>
            <li>CUST004: Multiple high-value pairs within 2 days with same counter parties (2 pairs - QUALIFIES)</li>
            <li>CUST005: $400,000 Dr (2024-01-20) and $400,000 Cr (2024-01-21) with same counter party "XYZ Ltd" (1 pair - QUALIFIES)</li>
        </ul>
        <p>Expected: 3 consolidated alerts (only customers with transactions ≥300,000)</p>
    </div>

    <script>
        // Simplified alert generation logic for testing
        function runAlertTest() {
            const testData = [
                {
                    'Transaction ID': 'TXN001',
                    'Trans Ref No': 'REF001',
                    'Customer Id': 'CUST001',
                    'Customer Name': 'John Smith',
                    'Account No': 'ACC001',
                    'Date': '2024-01-15',
                    'Tran Amount': 500000.00,
                    'Dr or Cr': 'Dr',
                    'Counter Party Name': 'ABC Corp'
                },
                {
                    'Transaction ID': 'TXN002',
                    'Trans Ref No': 'REF002',
                    'Customer Id': 'CUST001',
                    'Customer Name': 'John Smith',
                    'Account No': 'ACC001',
                    'Date': '2024-01-16',
                    'Tran Amount': 500000.00,
                    'Dr or Cr': 'Cr',
                    'Counter Party Name': 'ABC Corp'
                },
                // Below threshold - should NOT generate alert
                {
                    'Transaction ID': 'TXN003',
                    'Trans Ref No': 'REF003',
                    'Customer Id': 'CUST002',
                    'Customer Name': 'Jane Doe',
                    'Account No': 'ACC004',
                    'Date': '2024-01-16',
                    'Tran Amount': 100000.00,
                    'Dr or Cr': 'Dr',
                    'Counter Party Name': 'DEF Inc'
                },
                {
                    'Transaction ID': 'TXN004',
                    'Trans Ref No': 'REF004',
                    'Customer Id': 'CUST002',
                    'Customer Name': 'Jane Doe',
                    'Account No': 'ACC004',
                    'Date': '2024-01-17',
                    'Tran Amount': 100000.00,
                    'Dr or Cr': 'Cr',
                    'Counter Party Name': 'DEF Inc'
                },
                {
                    'Transaction ID': 'TXN005',
                    'Trans Ref No': 'REF005',
                    'Customer Id': 'CUST003',
                    'Customer Name': 'Bob Johnson',
                    'Account No': 'ACC007',
                    'Date': '2024-01-17',
                    'Tran Amount': 750000.00,
                    'Dr or Cr': 'Dr',
                    'Counter Party Name': 'GHI Corp'
                },
                {
                    'Transaction ID': 'TXN006',
                    'Trans Ref No': 'REF006',
                    'Customer Id': 'CUST003',
                    'Customer Name': 'Bob Johnson',
                    'Account No': 'ACC007',
                    'Date': '2024-01-17',
                    'Tran Amount': 750000.00,
                    'Dr or Cr': 'Cr',
                    'Counter Party Name': 'GHI Corp'
                },
                {
                    'Transaction ID': 'TXN009',
                    'Trans Ref No': 'REF009',
                    'Customer Id': 'CUST005',
                    'Customer Name': 'Charlie Wilson',
                    'Account No': 'ACC013',
                    'Date': '2024-01-20',
                    'Tran Amount': 400000.00,
                    'Dr or Cr': 'Dr',
                    'Counter Party Name': 'XYZ Ltd'
                },
                {
                    'Transaction ID': 'TXN010',
                    'Trans Ref No': 'REF010',
                    'Customer Id': 'CUST005',
                    'Customer Name': 'Charlie Wilson',
                    'Account No': 'ACC013',
                    'Date': '2024-01-21',
                    'Tran Amount': 400000.00,
                    'Dr or Cr': 'Cr',
                    'Counter Party Name': 'XYZ Ltd'
                },
                // Additional test data for multiple high-value pairs within two days
                {
                    'Transaction ID': 'TXN011',
                    'Trans Ref No': 'REF011',
                    'Customer Id': 'CUST004',
                    'Customer Name': 'Alice Brown',
                    'Account No': 'ACC010',
                    'Date': '2024-01-22',
                    'Tran Amount': 350000.00,
                    'Dr or Cr': 'Dr',
                    'Counter Party Name': 'PQR Corp'
                },
                {
                    'Transaction ID': 'TXN012',
                    'Trans Ref No': 'REF012',
                    'Customer Id': 'CUST004',
                    'Customer Name': 'Alice Brown',
                    'Account No': 'ACC010',
                    'Date': '2024-01-23',
                    'Tran Amount': 350000.00,
                    'Dr or Cr': 'Cr',
                    'Counter Party Name': 'PQR Corp'
                },
                {
                    'Transaction ID': 'TXN013',
                    'Trans Ref No': 'REF013',
                    'Customer Id': 'CUST004',
                    'Customer Name': 'Alice Brown',
                    'Account No': 'ACC010',
                    'Date': '2024-01-22',
                    'Tran Amount': 600000.00,
                    'Dr or Cr': 'Dr',
                    'Counter Party Name': 'STU Ltd'
                },
                {
                    'Transaction ID': 'TXN014',
                    'Trans Ref No': 'REF014',
                    'Customer Id': 'CUST004',
                    'Customer Name': 'Alice Brown',
                    'Account No': 'ACC010',
                    'Date': '2024-01-23',
                    'Tran Amount': 600000.00,
                    'Dr or Cr': 'Cr',
                    'Counter Party Name': 'STU Ltd'
                }
            ];

            const alerts = generateTestAlerts(testData);
            displayTestResults(alerts);
        }

        function generateTestAlerts(transactionData) {
            const alerts = [];

            // Group transactions by customer for two-day analysis
            const customerGroups = {};

            transactionData.forEach((transaction, index) => {
                const customerId = transaction['Customer Id'];
                const date = transaction['Date'];
                const amount = transaction['Tran Amount'];
                const drCr = transaction['Dr or Cr'];
                const counterParty = transaction['Counter Party Name'] || '';

                if (!customerId || !date || amount === 0 || !drCr || !counterParty) {
                    return; // Skip invalid transactions
                }

                if (!customerGroups[customerId]) {
                    customerGroups[customerId] = {
                        customerId,
                        transactions: []
                    };
                }

                customerGroups[customerId].transactions.push({
                    ...transaction,
                    originalIndex: index,
                    amount,
                    drCr,
                    counterParty,
                    date: new Date(date) // Convert to Date object for easier comparison
                });
            });
            
            // Detect two-day debit/credit alerts
            Object.values(customerGroups).forEach(group => {
                const { customerId, transactions } = group;

                // Sort transactions by date for easier processing
                transactions.sort((a, b) => a.date - b.date);

                // Find all two-day periods with matching debit/credit pairs
                const twoDayPeriods = findTwoDayPeriods(transactions);

                // Process each two-day period
                twoDayPeriods.forEach(period => {
                    const transactionPairs = findMatchingPairs(period.transactions);

                    if (transactionPairs.length > 0) {
                        const totalAmount = transactionPairs.reduce((sum, pair) => sum + pair.amount, 0);
                        const customerName = transactionPairs[0].debitTransaction['Customer Name'] || '';
                        const pairCount = transactionPairs.length;
                        const startDate = formatDateForDisplay(period.startDate);
                        const endDate = formatDateForDisplay(period.endDate);
                        const dateRange = startDate === endDate ? startDate : `${startDate} to ${endDate}`;

                        alerts.push({
                            id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                            type: 'two_day_debit_credit_consolidated',
                            title: 'Two-Day High-Value Debit/Credit Alert',
                            description: `Customer has ${pairCount} matching high-value debit/credit transaction pair${pairCount > 1 ? 's' : ''} (≥300,000 each) with same counter party within 2 days (${dateRange}) (Total: $${totalAmount.toLocaleString()})`,
                            customerId: customerId,
                            customerName: customerName,
                            dateRange: dateRange,
                            startDate: startDate,
                            endDate: endDate,
                            totalAmount: totalAmount,
                            pairCount: pairCount,
                            transactionPairs: transactionPairs,
                            status: 'new',
                            timestamp: new Date().toISOString()
                        });
                    }
                });
            });

            // Helper functions
            function findTwoDayPeriods(transactions) {
                const periods = [];
                const processedPeriods = new Set();

                for (let i = 0; i < transactions.length; i++) {
                    const currentDate = transactions[i].date;
                    const periodKey = formatDateForPeriod(currentDate);

                    if (processedPeriods.has(periodKey)) {
                        continue;
                    }

                    // Find all transactions within 2 days of current date
                    const periodTransactions = transactions.filter(transaction => {
                        const daysDiff = Math.abs((transaction.date - currentDate) / (1000 * 60 * 60 * 24));
                        return daysDiff <= 1; // Within 2 days (0-1 day difference)
                    });

                    if (periodTransactions.length > 1) {
                        periods.push({
                            startDate: new Date(Math.min(...periodTransactions.map(t => t.date))),
                            endDate: new Date(Math.max(...periodTransactions.map(t => t.date))),
                            transactions: periodTransactions
                        });
                        processedPeriods.add(periodKey);
                    }
                }

                return periods;
            }

            function findMatchingPairs(transactions) {
                const pairs = [];
                const MINIMUM_AMOUNT_THRESHOLD = 300000; // Minimum amount threshold for alerts

                // Group by amount and counter party
                const groups = {};
                transactions.forEach(transaction => {
                    // Only consider transactions that meet the minimum amount threshold
                    if (transaction.amount >= MINIMUM_AMOUNT_THRESHOLD) {
                        const key = `${transaction.amount}_${transaction.counterParty}`;
                        if (!groups[key]) {
                            groups[key] = { debits: [], credits: [] };
                        }

                        if (transaction.drCr === 'Dr') {
                            groups[key].debits.push(transaction);
                        } else if (transaction.drCr === 'Cr') {
                            groups[key].credits.push(transaction);
                        }
                    }
                });

                // Find matching pairs
                Object.entries(groups).forEach(([key, { debits, credits }]) => {
                    if (debits.length > 0 && credits.length > 0) {
                        // Create pairs for each debit/credit combination
                        debits.forEach(debit => {
                            credits.forEach(credit => {
                                // Double-check that both transactions meet the threshold
                                if (debit.amount >= MINIMUM_AMOUNT_THRESHOLD && credit.amount >= MINIMUM_AMOUNT_THRESHOLD) {
                                    pairs.push({
                                        debitTransaction: debit,
                                        creditTransaction: credit,
                                        amount: debit.amount,
                                        counterParty: debit.counterParty
                                    });
                                }
                            });
                        });
                    }
                });

                return pairs;
            }

            function formatDateForPeriod(date) {
                return date.toISOString().split('T')[0];
            }

            function formatDateForDisplay(date) {
                return date.toISOString().split('T')[0];
            }
            
            return alerts;
        }

        function displayTestResults(alerts) {
            const resultsDiv = document.getElementById('testResults');

            // Expected: 3 consolidated alerts (CUST001, CUST003, CUST004, CUST005 - CUST002 below threshold)
            if (alerts.length === 3) {
                resultsDiv.innerHTML = `
                    <div class="test-result">
                        <strong>✅ Test PASSED!</strong><br>
                        Generated ${alerts.length} consolidated high-value two-day alerts as expected.<br>
                        (CUST002 correctly excluded due to amount below 300,000 threshold)<br><br>
                        <strong>Alert Details:</strong><br>
                        ${alerts.map(alert =>
                            `• ${alert.customerId} - ${alert.customerName}: ${alert.pairCount} pair${alert.pairCount > 1 ? 's' : ''}, Total: $${alert.totalAmount.toLocaleString()} (${alert.dateRange})`
                        ).join('<br>')}
                    </div>
                `;
            } else {
                resultsDiv.innerHTML = `
                    <div class="test-error">
                        <strong>❌ Test FAILED!</strong><br>
                        Expected 3 consolidated high-value two-day alerts, but generated ${alerts.length} alerts.<br>
                        (Should exclude CUST002 due to amount below 300,000 threshold)<br><br>
                        <strong>Generated Alerts:</strong><br>
                        ${alerts.map(alert =>
                            `• ${alert.customerId} - ${alert.customerName}: ${alert.pairCount || 1} pair${(alert.pairCount || 1) > 1 ? 's' : ''}, Total: $${(alert.totalAmount || alert.amount || 0).toLocaleString()} (${alert.dateRange || alert.date || 'N/A'})`
                        ).join('<br>')}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
