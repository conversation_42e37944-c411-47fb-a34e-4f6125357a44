# Sample Data Files

This directory contains sample data files for testing the AML/CFT Transaction Monitoring Compliance Division Dashboard.

## Files

### test-alert-data-sample.xlsx
- **Description**: Sample Excel file with transaction data for testing alert generation
- **Format**: Excel (.xlsx)
- **Usage**: Upload this file through the dashboard to test the transaction analysis and alert generation features

### test-alert-data.csv
- **Description**: Sample CSV file with transaction data
- **Format**: Comma-separated values (.csv)
- **Usage**: Alternative format for testing data import functionality

## Data Structure

The sample files contain the following columns (39 total):
1. Transaction ID
2. Trans Ref No
3. Source System
4. UCIC
5. Customer Id
6. Customer Name
7. Account No
8. Account Open Date
9. Product Type
10. Product Sub-type
11. Branch
12. Date
13. Tran Amount
14. Account Balance
15. Original Amount
16. Tran Currency
17. Dr or Cr
18. Quantity
19. Unit Price
20. Transaction Type
21. Channel Type
22. Transaction Sub Type
23. Channel Sub Type
24. Instrument Type
25. Instrument No
26. Purpose Code
27. Merchant Type
28. Merchant ID
29. Counter Party Name
30. Counter Customer ID
31. Counter Account No.
32. Counter Bank
33. Counter Country
34. Remarks
35. Particulars
36. Transaction Location Id
37. Approved User Id
38. Entry User Id
39. Posted User Id

## Usage Instructions

1. Navigate to the main dashboard (index.html)
2. Use the drag-and-drop upload area or browse button
3. Select one of the sample files from this directory
4. The dashboard will process the data and generate alerts based on configured rules

## Note

These files are for testing purposes only and contain synthetic data designed to demonstrate the alert generation capabilities of the system.
