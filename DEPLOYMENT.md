# AML/CFT Transaction Monitoring Compliance Division
## Deployment Guide

### Production-Ready File Structure

The Transaction Analysis Dashboard has been reorganized into a professional, production-ready structure:

```
Transaction Analysis Dashboard/
├── index.html              # Main application entry point
├── css/
│   └── styles.css          # Professional styling and UI components
├── js/
│   └── script.js           # Core application logic and functionality
├── assets/                 # Static assets directory (ready for images, icons, etc.)
├── data/
│   ├── test-alert-data-sample.xlsx  # Sample Excel data for testing
│   ├── test-alert-data.csv          # Sample CSV data for testing
│   └── README.md                    # Data files documentation
├── dev/
│   ├── create-test-excel.html       # Development utility for test data creation
│   ├── sample-data-generator.html   # Advanced sample data generator
│   ├── test-alert-system.html       # Alert system testing interface
│   ├── test-validation.html         # Data validation testing
│   └── README.md                    # Development files documentation
├── README.md               # Main project documentation
└── DEPLOYMENT.md           # This deployment guide
```

### Deployment Instructions

#### For Local Development
1. Open `index.html` directly in a modern web browser
2. All features will work immediately (requires internet for CDN resources)

#### For Web Server Deployment
1. Upload all files maintaining the folder structure
2. Ensure the web server serves static files correctly
3. Set `index.html` as the default document
4. Configure MIME types for .xlsx and .xls files if needed

#### For Intranet Deployment
1. Copy the entire folder structure to the web server
2. Update any absolute paths if necessary (all paths are currently relative)
3. Test file upload functionality with sample data from the `data/` folder

### Security Considerations

- **Client-side Processing**: All data processing occurs in the browser
- **No Server Dependencies**: No backend server or database required
- **CDN Dependencies**: Requires internet access for external libraries
- **File Access**: Uses standard HTML5 File API for secure file handling

### Browser Requirements

- **Minimum Requirements**: ES6 support, File API, Web Workers
- **Recommended Browsers**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **Internet Connection**: Required for CDN resources (SheetJS, Font Awesome, Google Fonts)

### Performance Optimization

- **File Size Limits**: 50MB maximum for Excel files
- **Memory Usage**: Optimized for files up to 100,000 transactions
- **Processing Speed**: Client-side processing scales with device performance

### Maintenance

- **Updates**: Replace files while maintaining folder structure
- **Backups**: Backup entire folder structure for version control
- **Testing**: Use files in `dev/` folder for testing new features

### Troubleshooting

#### Common Issues
1. **Blank Page**: Check browser console for JavaScript errors
2. **Styling Issues**: Verify CSS file path in index.html
3. **Upload Problems**: Ensure file size limits and format requirements
4. **CDN Issues**: Check internet connection for external resources

#### File Path Verification
- CSS: `css/styles.css`
- JavaScript: `js/script.js`
- Sample Data: `data/test-alert-data-sample.xlsx`

### Production Checklist

- [ ] All file paths are relative
- [ ] CSS and JavaScript files load correctly
- [ ] Sample data files are accessible
- [ ] Development files are separated in `dev/` folder
- [ ] Documentation is complete and up-to-date
- [ ] Browser compatibility tested
- [ ] File upload functionality verified
- [ ] Alert system functionality confirmed
- [ ] Export functionality working

### Support

For technical support or questions about deployment, refer to the main README.md file or contact the AML/CFT Compliance Team.

---

**Version**: 2.0  
**Last Updated**: Production Reorganization  
**Deployment Status**: Ready for Production
