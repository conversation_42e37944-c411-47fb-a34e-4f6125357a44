# Development and Testing Files

This directory contains development and testing files for the AML/CFT Transaction Monitoring Compliance Division Dashboard.

## Files

### create-test-excel.html
- **Description**: Utility for creating test Excel files with sample transaction data
- **Usage**: Open in browser to generate custom test data files
- **Purpose**: Development tool for creating various test scenarios

### sample-data-generator.html
- **Description**: Advanced sample data generator with configurable parameters
- **Usage**: Open in browser to generate custom transaction datasets
- **Features**: Configurable transaction counts, date ranges, and alert scenarios

### test-alert-system.html
- **Description**: Standalone alert system testing interface
- **Usage**: Open in browser to test alert generation logic independently
- **Purpose**: Unit testing for alert detection algorithms

### test-validation.html
- **Description**: Data validation testing interface
- **Usage**: Open in browser to test column validation and data processing
- **Purpose**: Testing file format validation and error handling

## Development Workflow

1. **Data Generation**: Use `sample-data-generator.html` to create test datasets
2. **Alert Testing**: Use `test-alert-system.html` to verify alert logic
3. **Validation Testing**: Use `test-validation.html` to test file processing
4. **Excel Creation**: Use `create-test-excel.html` for specific test scenarios

## Note

These files are for development and testing purposes only. They are not part of the production application and should not be deployed to production environments.

## Usage

Each HTML file can be opened directly in a web browser and functions independently of the main application. They use the same underlying libraries (SheetJS, etc.) as the main application for consistency in testing.
