<!DOCTYPE html>
<!--
    AML/CFT Transaction Monitoring Compliance Division
    Transaction Analysis Dashboard - Main Application

    Professional banking-grade web application for transaction analysis,
    alert management, and compliance monitoring.

    @version 1.0.0
    <AUTHOR> Compliance Team
    @description Main HTML file for the Transaction Analysis Dashboard
-->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AML/CFT Transaction Monitoring Compliance Division - Transaction Analysis Dashboard</title>
    <link rel="stylesheet" href="css/styles.css">
    <!-- SheetJS CDN for Excel parsing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts for professional typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Professional Banking Header -->
        <header class="banking-header">
            <div class="header-top-bar">
                <div class="header-container">
                    <div class="brand-section">
                        <div class="brand-logo">
                            <div class="logo-icon">
                                <i class="fas fa-university"></i>
                            </div>
                            <div class="brand-text">
                                <span class="brand-name">AML/CFT TRANSACTION MONITORING</span>
                                <span class="brand-division">COMPLIANCE DIVISION</span>
                            </div>
                        </div>
                    </div>
                    <div class="header-info">
                        <div class="system-status">
                            <span class="status-indicator online"></span>
                            <span class="status-text">System Online</span>
                        </div>
                        <div class="current-time" id="currentTime">
                            <!-- Time will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="header-main">
                <div class="header-container">
                    <div class="title-section">
                        <div class="title-icon-group">
                            <div class="primary-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="secondary-icons">
                                <i class="fas fa-shield-alt"></i>
                                <i class="fas fa-analytics"></i>
                            </div>
                        </div>
                        <div class="title-content">
                            <h1 class="main-title">Transaction Analysis Dashboard</h1>
                            <p class="title-subtitle">Professional Banking Transaction Analysis & Risk Management Platform</p>
                            <div class="title-features">
                                <span class="feature-badge">
                                    <i class="fas fa-lock"></i> Secure
                                </span>
                                <span class="feature-badge">
                                    <i class="fas fa-tachometer-alt"></i> Real-time
                                </span>
                                <span class="feature-badge">
                                    <i class="fas fa-chart-bar"></i> Analytics
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="header-actions">
                        <div class="quick-stats">
                            <div class="quick-stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-label">Data Status</span>
                                    <span class="stat-value" id="headerDataStatus">Ready</span>
                                </div>
                            </div>
                            <div class="quick-stat-item">
                                <div class="stat-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-label">Active Alerts</span>
                                    <span class="stat-value" id="headerAlertCount">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Navigation Tabs -->
        <nav class="nav-tabs">
            <div class="nav-container">
                <button class="nav-tab active" id="transactionsTab">
                    <i class="fas fa-table"></i> Transactions
                </button>
                <button class="nav-tab" id="alertsTab">
                    <i class="fas fa-exclamation-triangle"></i> Alerts
                    <span class="alert-badge" id="alertBadge" style="display: none;">0</span>
                </button>
                <button class="nav-tab" id="ruleConfigTab">
                    <i class="fas fa-cogs"></i> Rule Configuration
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Transactions View -->
            <div class="view-container" id="transactionsView">
                <!-- Upload Section -->
                <section class="upload-section">
                <div class="upload-card">
                    <h2><i class="fas fa-upload"></i> Upload Transaction Data</h2>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <i class="fas fa-file-excel upload-icon"></i>
                            <h3>Drag & Drop Excel File Here</h3>
                            <p>or <span class="browse-link" id="browseLink">browse to select file</span></p>
                            <p class="file-info">Supports .xlsx and .xls files</p>
                        </div>
                        <input type="file" id="fileInput" accept=".xlsx,.xls" hidden>
                    </div>
                    <div class="upload-status" id="uploadStatus"></div>
                    <div class="upload-progress" id="uploadProgress">
                        <div class="progress-bar-container">
                            <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                        </div>
                        <div class="progress-text" id="progressText">Processing...</div>
                    </div>
                </div>
            </section>

            <!-- Statistics Section -->
            <section class="stats-section" id="statsSection" style="display: none;">
                <h2><i class="fas fa-chart-bar"></i> Transaction Summary</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-list"></i></div>
                        <div class="stat-content">
                            <h3 id="totalTransactions">0</h3>
                            <p>Total Transactions</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-arrow-up text-success"></i></div>
                        <div class="stat-content">
                            <h3 id="creditAmount">$0</h3>
                            <p>Total Credits</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-arrow-down text-danger"></i></div>
                        <div class="stat-content">
                            <h3 id="debitAmount">$0</h3>
                            <p>Total Debits</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-balance-scale"></i></div>
                        <div class="stat-content">
                            <h3 id="netAmount">$0</h3>
                            <p>Net Amount</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-calculator"></i></div>
                        <div class="stat-content">
                            <h3 id="totalAmount">$0</h3>
                            <p>Total Transaction Amount</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Data Preview Section -->
            <section class="data-section" id="dataSection" style="display: none;">
                <div class="data-header">
                    <h2><i class="fas fa-table"></i> Transaction Data Preview</h2>
                    <div class="data-controls">
                        <button class="btn btn-secondary" id="exportBtn">
                            <i class="fas fa-download"></i> Export Data
                        </button>
                        <button class="btn btn-outline" id="clearBtn">
                            <i class="fas fa-trash"></i> Clear Data
                        </button>
                    </div>
                </div>

                <!-- Enhanced Table Controls -->
                <div class="table-controls">
                    <div class="table-search">
                        <i class="fas fa-search"></i>
                        <input type="text" id="tableSearchInput" placeholder="Search transactions...">
                    </div>
                    <div class="table-filter-group">
                        <select id="drCrFilter" class="table-filter-select">
                            <option value="">All Types</option>
                            <option value="Dr">Debit</option>
                            <option value="Cr">Credit</option>
                        </select>
                        <select id="branchFilter" class="table-filter-select">
                            <option value="">All Branches</option>
                        </select>
                        <button class="btn btn-sm btn-outline" id="clearTableFiltersBtn">
                            <i class="fas fa-times"></i> Clear
                        </button>
                    </div>
                </div>

                <div class="table-container">
                    <table class="data-table" id="dataTable">
                        <thead>
                            <tr>
                                <th>Transaction ID</th>
                                <th>Trans Ref No</th>
                                <th>Source System</th>
                                <th>UCIC</th>
                                <th>Customer ID</th>
                                <th>Customer Name</th>
                                <th>Account No</th>
                                <th>Account Open Date</th>
                                <th>Product Type</th>
                                <th>Product Sub-type</th>
                                <th>Branch</th>
                                <th>Date</th>
                                <th>Tran Amount</th>
                                <th>Account Balance</th>
                                <th>Original Amount</th>
                                <th>Tran Currency</th>
                                <th>Dr or Cr</th>
                                <th>Quantity</th>
                                <th>Unit Price</th>
                                <th>Transaction Type</th>
                                <th>Channel Type</th>
                                <th>Transaction Sub Type</th>
                                <th>Channel Sub Type</th>
                                <th>Instrument Type</th>
                                <th>Instrument No</th>
                                <th>Purpose Code</th>
                                <th>Merchant Type</th>
                                <th>Merchant ID</th>
                                <th>Counter Party Name</th>
                                <th>Counter Customer ID</th>
                                <th>Counter Account No.</th>
                                <th>Counter Bank</th>
                                <th>Counter Country</th>
                                <th>Remarks</th>
                                <th>Particulars</th>
                                <th>Transaction Location ID</th>
                                <th>Approved User ID</th>
                                <th>Entry User ID</th>
                                <th>Posted User ID</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                        </tbody>
                    </table>
                </div>
                <div class="pagination" id="pagination">
                    <button class="btn btn-outline" id="prevBtn" disabled>
                        <i class="fas fa-chevron-left"></i> Previous
                    </button>
                    <span class="page-info" id="pageInfo">Page 1 of 1</span>
                    <button class="btn btn-outline" id="nextBtn" disabled>
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </section>
            </div>

            <!-- Alerts View -->
            <div class="view-container" id="alertsView" style="display: none;">
                <!-- Alert Summary Section -->
                <section class="alert-summary-section">
                    <h2><i class="fas fa-shield-alt"></i> Alert Management Dashboard</h2>
                    <div class="alert-stats-grid">
                        <div class="alert-stat-card">
                            <div class="alert-stat-icon new"><i class="fas fa-exclamation-circle"></i></div>
                            <div class="alert-stat-content">
                                <h3 id="newAlertsCount">0</h3>
                                <p>New Alerts</p>
                            </div>
                        </div>
                        <div class="alert-stat-card">
                            <div class="alert-stat-icon reviewed"><i class="fas fa-eye"></i></div>
                            <div class="alert-stat-content">
                                <h3 id="reviewedAlertsCount">0</h3>
                                <p>Reviewed</p>
                            </div>
                        </div>
                        <div class="alert-stat-card">
                            <div class="alert-stat-icon dismissed"><i class="fas fa-times-circle"></i></div>
                            <div class="alert-stat-content">
                                <h3 id="dismissedAlertsCount">0</h3>
                                <p>Dismissed</p>
                            </div>
                        </div>
                        <div class="alert-stat-card">
                            <div class="alert-stat-icon total"><i class="fas fa-list"></i></div>
                            <div class="alert-stat-content">
                                <h3 id="totalAlertsCount">0</h3>
                                <p>Total Alerts</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Alert Controls Section -->
                <section class="alert-controls-section">
                    <div class="alert-controls-header">
                        <h3><i class="fas fa-filter"></i> Alert Filters & Actions</h3>
                        <div class="alert-actions">
                            <button class="btn btn-secondary" id="exportAlertsBtn">
                                <i class="fas fa-download"></i> Export Alerts
                            </button>
                            <button class="btn btn-outline" id="clearAlertsBtn">
                                <i class="fas fa-trash"></i> Clear All Alerts
                            </button>
                        </div>
                    </div>
                    <div class="alert-filters">
                        <div class="filter-group">
                            <label for="statusFilter">Status:</label>
                            <select id="statusFilter" class="filter-select">
                                <option value="all">All Statuses</option>
                                <option value="new">New</option>
                                <option value="reviewed">Reviewed</option>
                                <option value="dismissed">Dismissed</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="severityFilter">Severity:</label>
                            <select id="severityFilter" class="filter-select">
                                <option value="all">All Severities</option>
                                <option value="critical">Critical</option>
                                <option value="high">High</option>
                                <option value="medium">Medium</option>
                                <option value="low">Low</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="dateFromFilter">Date From:</label>
                            <input type="date" id="dateFromFilter" class="filter-input">
                        </div>
                        <div class="filter-group">
                            <label for="dateToFilter">Date To:</label>
                            <input type="date" id="dateToFilter" class="filter-input">
                        </div>
                        <div class="filter-group">
                            <label for="customerFilter">Customer ID:</label>
                            <input type="text" id="customerFilter" class="filter-input" placeholder="Enter Customer ID">
                        </div>
                        <button class="btn btn-primary" id="applyFiltersBtn">
                            <i class="fas fa-search"></i> Apply Filters
                        </button>
                        <button class="btn btn-outline" id="clearFiltersBtn">
                            <i class="fas fa-eraser"></i> Clear
                        </button>
                    </div>
                </section>

                <!-- Alerts List Section -->
                <section class="alerts-list-section">
                    <div class="alerts-header">
                        <h3><i class="fas fa-list-ul"></i> Alert Details</h3>
                        <div class="bulk-actions" id="bulkActions" style="display: none;">
                            <button class="btn btn-sm btn-secondary" id="bulkReviewBtn">
                                <i class="fas fa-eye"></i> Mark as Reviewed
                            </button>
                            <button class="btn btn-sm btn-outline" id="bulkDismissBtn">
                                <i class="fas fa-times"></i> Dismiss
                            </button>
                        </div>
                    </div>
                    <div class="alerts-container" id="alertsContainer">
                        <div class="no-alerts-message" id="noAlertsMessage">
                            <i class="fas fa-shield-alt"></i>
                            <h4>No Alerts Found</h4>
                            <p>Upload transaction data to generate alerts, or adjust your filters.</p>
                        </div>
                    </div>
                    <div class="alert-pagination" id="alertPagination" style="display: none;">
                        <button class="btn btn-outline" id="alertPrevBtn" disabled>
                            <i class="fas fa-chevron-left"></i> Previous
                        </button>
                        <span class="page-info" id="alertPageInfo">Page 1 of 1</span>
                        <button class="btn btn-outline" id="alertNextBtn" disabled>
                            Next <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </section>
            </div>

            <!-- Rule Configuration View -->
            <div class="view-container" id="ruleConfigView" style="display: none;">
                <!-- Rule Configuration Header -->
                <section class="rule-config-header-section">
                    <h2><i class="fas fa-cogs"></i> Alert Rule Configuration</h2>
                    <p class="rule-config-description">Configure the alert detection criteria for the Two-Day High-Value Debit/Credit Alert rule. Changes will be applied immediately and will regenerate alerts based on the new settings.</p>
                </section>

                <!-- Current Rule Display -->
                <section class="current-rule-section">
                    <h3><i class="fas fa-info-circle"></i> Current Rule Settings</h3>
                    <div class="current-rule-display">
                        <div class="rule-info-grid">
                            <div class="rule-info-item">
                                <div class="rule-info-label">Rule Name</div>
                                <div class="rule-info-value">Two-Day High-Value Debit/Credit Alert</div>
                            </div>
                            <div class="rule-info-item">
                                <div class="rule-info-label">Time Window</div>
                                <div class="rule-info-value" id="currentTimeWindow">2 days (consecutive days)</div>
                            </div>
                            <div class="rule-info-item">
                                <div class="rule-info-label">Minimum Amount Threshold</div>
                                <div class="rule-info-value" id="currentAmountThreshold">300,000</div>
                            </div>
                            <div class="rule-info-item">
                                <div class="rule-info-label">Counter Party Matching</div>
                                <div class="rule-info-value" id="currentCounterPartyMatching">Required (exact match)</div>
                            </div>
                            <div class="rule-info-item">
                                <div class="rule-info-label">Transaction Types</div>
                                <div class="rule-info-value">Debit (Dr) and Credit (Cr) pairs</div>
                            </div>
                            <div class="rule-info-item">
                                <div class="rule-info-label">Alert Consolidation</div>
                                <div class="rule-info-value" id="currentConsolidation">Enabled (one alert per customer per period)</div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Rule Configuration Form -->
                <section class="rule-config-form-section">
                    <h3><i class="fas fa-edit"></i> Modify Rule Settings</h3>
                    <form id="ruleConfigForm" class="rule-config-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="amountThreshold">Minimum Amount Threshold</label>
                                <input type="number" id="amountThreshold" class="form-input" min="1" step="1000" required>
                                <div class="form-help">Minimum transaction amount to trigger alerts (both debit and credit must meet this threshold)</div>
                                <div class="form-error" id="amountThresholdError"></div>
                            </div>
                            <div class="form-group">
                                <label for="timeWindow">Time Window (Days)</label>
                                <select id="timeWindow" class="form-select" required>
                                    <option value="1">1 Day (Same Day)</option>
                                    <option value="2" selected>2 Days (Consecutive Days)</option>
                                    <option value="3">3 Days</option>
                                    <option value="4">4 Days</option>
                                    <option value="5">5 Days</option>
                                    <option value="6">6 Days</option>
                                    <option value="7">7 Days</option>
                                </select>
                                <div class="form-help">Maximum number of days between debit and credit transactions</div>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="counterPartyMatching" checked>
                                    <label for="counterPartyMatching">Require Counter Party Matching</label>
                                </div>
                                <div class="form-help">When enabled, debit and credit transactions must have identical counter party names</div>
                            </div>
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" id="alertConsolidation" checked>
                                    <label for="alertConsolidation">Enable Alert Consolidation</label>
                                </div>
                                <div class="form-help">When enabled, multiple transaction pairs for the same customer/period are grouped into one alert</div>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn btn-outline" id="resetToDefaultsBtn">
                                <i class="fas fa-undo"></i> Reset to Defaults
                            </button>
                            <button type="submit" class="btn btn-primary" id="saveRuleConfigBtn">
                                <i class="fas fa-save"></i> Save Rule Configuration
                            </button>
                        </div>
                    </form>
                </section>

                <!-- Rule Application Status -->
                <section class="rule-status-section" id="ruleStatusSection" style="display: none;">
                    <div class="rule-status-message" id="ruleStatusMessage">
                        <!-- Status messages will be displayed here -->
                    </div>
                </section>
            </div>
        </main>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-content">
                <div class="spinner"></div>
                <p>Processing transaction data...</p>
            </div>
        </div>

        <!-- Error Modal -->
        <div class="modal" id="errorModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-exclamation-triangle"></i> Error</h3>
                    <button class="close-btn" id="closeErrorModal">&times;</button>
                </div>
                <div class="modal-body">
                    <p id="errorMessage"></p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-primary" id="errorOkBtn">OK</button>
                </div>
            </div>
        </div>

        <!-- Alert Detail Modal -->
        <div class="modal" id="alertDetailModal">
            <div class="modal-content alert-modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-exclamation-triangle"></i> Alert Details</h3>
                    <button class="close-btn" id="closeAlertModal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="alert-detail-content" id="alertDetailContent">
                        <!-- Alert details will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="markReviewedBtn">
                        <i class="fas fa-eye"></i> Mark as Reviewed
                    </button>
                    <button class="btn btn-outline" id="dismissAlertBtn">
                        <i class="fas fa-times"></i> Dismiss
                    </button>
                    <button class="btn btn-primary" id="closeAlertDetailBtn">Close</button>
                </div>
            </div>
        </div>

        <!-- Alert Notes Modal -->
        <div class="modal" id="alertNotesModal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-sticky-note"></i> Add Note</h3>
                    <button class="close-btn" id="closeNotesModal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="alertNoteText">Note:</label>
                        <textarea id="alertNoteText" class="form-textarea" rows="4" placeholder="Enter your note here..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline" id="cancelNoteBtn">Cancel</button>
                    <button class="btn btn-primary" id="saveNoteBtn">Save Note</button>
                </div>
            </div>
        </div>

        <!-- Column Mapping Modal -->
        <div class="modal" id="columnMappingModal">
            <div class="modal-content column-mapping-modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-columns"></i> Column Mapping Information</h3>
                    <button class="close-btn" id="closeColumnMappingModal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="column-mapping-info">
                        <div class="mapping-notice">
                            <i class="fas fa-info-circle"></i>
                            <p>Your file has been processed successfully! The columns were not in the expected order, but we've automatically mapped them correctly.</p>
                        </div>
                        <div class="column-mapping-content" id="columnMappingContent">
                            <!-- Column mapping details will be populated here -->
                        </div>
                        <div class="mapping-actions">
                            <div class="mapping-action-item">
                                <i class="fas fa-check-circle text-success"></i>
                                <span>Data processed with flexible column mapping</span>
                            </div>
                            <div class="mapping-action-item">
                                <i class="fas fa-download"></i>
                                <span>You can export the data in the standard column order</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="exportReorderedBtn">
                        <i class="fas fa-download"></i> Export with Standard Order
                    </button>
                    <button class="btn btn-primary" id="closeColumnMappingBtn">Continue</button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/script.js"></script>
</body>
</html>
