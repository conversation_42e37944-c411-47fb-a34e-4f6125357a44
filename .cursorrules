# Transaction Analysis Dashboard - Project Rules

## Project Patterns

### File Structure
- All HTML files in root directory
- CSS files in css/ directory
- JavaScript files in js/ directory
- Assets in assets/ directory
- Development utilities in dev/ directory
- Sample data in data/ directory

### Code Organization
- Vanilla JavaScript without frameworks
- Modular code structure
- Clear separation of concerns
- Consistent naming conventions

### Development Workflow
- Direct file editing
- Browser-based testing
- No build process
- CDN dependencies

## User Preferences

### Code Style
- Consistent indentation (2 spaces)
- Clear variable naming
- Comprehensive comments
- Modular functions

### Documentation
- Detailed README files
- Inline code comments
- Function documentation
- Change tracking

### Testing
- Browser-based testing
- Manual verification
- Sample data usage
- Error checking

## Project Intelligence

### Critical Paths
1. File Upload
   - Excel file validation
   - Data processing
   - Alert generation
   - UI updates

2. Alert System
   - Pattern detection
   - Alert consolidation
   - Status management
   - Export functionality

### Known Challenges
1. Performance
   - Large file processing
   - Memory management
   - Browser limitations
   - Processing time

2. Compatibility
   - Browser support
   - Feature detection
   - Mobile devices
   - Screen sizes

### Best Practices
1. Code
   - Modular design
   - Error handling
   - Performance optimization
   - Security measures

2. Documentation
   - Clear structure
   - Regular updates
   - Comprehensive coverage
   - Version tracking

## Tool Usage

### Development
- Text editor/IDE
- Browser developer tools
- Git version control
- Testing utilities

### Testing
- Sample data files
- Browser testing
- Performance monitoring
- Error checking

### Deployment
- Web server
- Static file serving
- MIME type configuration
- Access control

## Project Evolution

### Current Focus
- Performance optimization
- Error handling
- User experience
- Documentation

### Future Direction
- Advanced analytics
- Custom rules
- Machine learning
- Risk assessment

### Maintenance
- Regular updates
- Bug fixes
- Performance improvements
- Security patches 