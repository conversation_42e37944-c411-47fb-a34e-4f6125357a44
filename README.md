# AML/CFT Transaction Monitoring Compliance Division
## Transaction Analysis Dashboard with Alert Management System

A professional, production-ready web application for analyzing banking transaction data from Excel files with comprehensive alert management capabilities. Built for the AML/CFT Transaction Monitoring Compliance Division.

## Features

### Core Functionality
- **Excel File Upload**: Drag-and-drop or browse to select .xlsx/.xls files
- **Data Validation**: Validates all 39 required columns and data types
- **Real-time Processing**: Client-side processing with no server dependencies
- **Professional UI**: Banking-grade interface with responsive design
- **Data Export**: Export processed data back to Excel format

### Alert Management System
- **Two-Day High-Value Debit/Credit Detection**: Automatically identifies suspicious high-value debit and credit transactions (≥300,000) with identical amounts and matching counter parties within a two-day period
- **Consolidated Alert Approach**: Groups all suspicious transaction pairs by customer and two-day period to reduce alert noise
- **Alert Dashboard**: Comprehensive alert management interface with statistics and filtering
- **Alert Status Management**: Mark alerts as new, reviewed, or dismissed
- **Alert Notes**: Add comments and notes to alerts for investigation tracking
- **Bulk Operations**: Perform actions on multiple alerts simultaneously
- **Alert Export**: Export alerts to Excel/CSV format for reporting
- **Real-time Notifications**: Alert badge notifications for new alerts

### Required Excel Columns (in exact order)
1. Transaction ID
2. Trans Ref No
3. Source System
4. UCIC
5. Customer Id
6. Customer Name
7. Account No
8. Account Open Date
9. Product Type
10. Product Sub-type
11. Branch
12. Date
13. Tran Amount
14. Account Balance
15. Original Amount
16. Tran Currency
17. Dr or Cr
18. Quantity
19. Unit Price
20. Transaction Type
21. Channel Type
22. Transaction Sub Type
23. Channel Sub Type
24. Instrument Type
25. Instrument No
26. Purpose Code
27. Merchant Type
28. Merchant ID
29. Counter Party Name
30. Counter Customer ID
31. Counter Account No.
32. Counter Bank
33. Counter Country
34. Remarks
35. Particulars
36. Transaction Location Id
37. Approved User Id
38. Entry User Id
39. Posted User Id

### Dashboard Components
- **Tabbed Navigation**: Switch between transaction analysis, alert management, and rule configuration
- **Upload Section**: File upload with drag-and-drop support
- **Statistics Summary**:
  - Total transaction count
  - Total credits and debits
  - Net amount calculation
  - Alert counts by status
- **Data Preview**: Paginated table view of transaction data
- **Alert Management**: Comprehensive alert dashboard with filtering and actions
- **Rule Configuration**: Real-time alert rule customization interface
- **Export Functionality**: Download processed data and alerts as Excel files

## Technical Specifications

### Technologies Used
- **HTML5**: Semantic markup and structure
- **CSS3**: Professional styling with responsive design
- **Vanilla JavaScript**: Core functionality without frameworks
- **SheetJS (XLSX)**: Excel file parsing via CDN
- **Font Awesome**: Professional icons via CDN

### Browser Compatibility
- Chrome (recommended)
- Firefox
- Safari
- Microsoft Edge

### File Requirements
- **Supported formats**: .xlsx, .xls
- **Maximum file size**: 50MB
- **Required structure**: First row must contain exact column headers
- **Data validation**: Automatic validation for dates, amounts, and Dr/Cr fields

## Usage Instructions

### 1. Upload Excel File
- Click the upload area or drag and drop an Excel file
- File must contain all 39 required columns in the specified order
- System will validate file format and structure

### 2. Review Data
- View transaction statistics in the summary cards
- Browse transaction data in the paginated table
- Use pagination controls to navigate through large datasets

### 3. Export Data
- Click "Export Data" to download processed transactions
- Exported file includes all validated and formatted data
- Filename includes timestamp for easy identification

### 4. Alert Management
- Click the "Alerts" tab to access the alert management dashboard
- Review alert statistics and filter alerts by status, date, or customer
- Click "View Details" to see comprehensive alert information
- Mark alerts as reviewed or dismissed
- Add investigation notes to alerts
- Export alerts for compliance reporting

### 5. Rule Configuration
- Click the "Rule Configuration" tab to customize alert detection criteria
- Modify minimum amount threshold, time window, and matching requirements
- Changes are applied immediately and automatically regenerate alerts
- Reset to default settings if needed
- Real-time validation ensures proper configuration

### 6. Clear Data
- Use "Clear Data" button to reset the dashboard and clear all alerts
- Confirmation dialog prevents accidental data loss

## Data Validation

### Automatic Validation
- **Amounts**: Removes currency symbols, validates numeric values
- **Dates**: Handles Excel date formats and standard date strings
- **Dr/Cr**: Normalizes debit/credit indicators
- **Missing Data**: Handles empty cells gracefully

### Error Handling
- **File Format**: Validates Excel file types
- **Column Structure**: Ensures all required columns are present
- **Data Quality**: Provides warnings for invalid data
- **User Feedback**: Clear error messages and status updates

## Security Features
- **Client-side Processing**: No data transmitted to external servers
- **File Size Limits**: Prevents processing of excessively large files
- **Input Validation**: Comprehensive validation of all user inputs
- **Error Boundaries**: Graceful handling of processing errors

## Keyboard Shortcuts
- **Escape**: Close error dialogs
- **Ctrl+E**: Export data (when available)

## Alert System Details

### Alert Types
1. **Two-Day High-Value Debit/Credit Alert (Consolidated)**: Detects when a customer has both debit and credit transactions within a two-day period with identical amounts (≥300,000) and matching counter parties, consolidating all suspicious pairs for that customer/period into a single alert

### Alert Detection Criteria
- **Two-Day Window**: Transactions must occur within consecutive days (maximum 1-day gap)
- **Minimum Amount Threshold**: Both debit and credit transactions must be 300,000 or above
- **Identical Amounts**: Debit and credit transactions must have exactly matching amounts
- **Matching Counter Parties**: Both transactions must involve the same counter party
- **Flexible Order**: Alert triggers regardless of whether debit or credit occurs first

### Alert Consolidation Logic
- **Customer-Based Grouping**: Creates one alert per customer per two-day period, regardless of the number of matching transaction pairs
- **Comprehensive Coverage**: Captures all suspicious transaction pairs while reducing alert noise
- **Detailed Breakdown**: Alert details show all individual transaction pairs when investigated
- **Smart Summarization**: Alert descriptions include total amounts, pair counts, and date ranges

### Alert Management Features
- **Alert Statistics**: Real-time counts of new, reviewed, dismissed, and total alerts
- **Advanced Filtering**: Filter alerts by status, date range, and customer ID
- **Alert Details**: Comprehensive view showing all transaction pairs for the customer/date
- **Status Tracking**: Track alert lifecycle from new to reviewed/dismissed
- **Notes System**: Add investigative notes and comments to alerts
- **Bulk Actions**: Review or dismiss multiple alerts at once
- **Export Capabilities**: Export filtered alerts with full transaction details and pair breakdowns

### Rule Configuration Features
- **Real-time Configuration**: Modify alert detection criteria without code changes
- **Configurable Parameters**:
  - Minimum Amount Threshold (any positive value)
  - Time Window (1-7 days)
  - Counter Party Matching (required/optional)
  - Alert Consolidation (enabled/disabled)
- **Immediate Application**: Changes take effect instantly with automatic alert regeneration
- **Form Validation**: Client-side validation ensures proper configuration values
- **Reset Functionality**: Restore default settings with one click
- **Status Feedback**: Clear success/error messages for all configuration changes

## File Structure
```
Transaction Analysis Dashboard/
├── index.html              # Main application entry point
├── css/
│   └── styles.css          # Professional styling and UI components
├── js/
│   └── script.js           # Core application logic and functionality
├── assets/                 # Static assets (images, icons, etc.)
├── data/
│   ├── test-alert-data-sample.xlsx  # Sample Excel data for testing
│   ├── test-alert-data.csv          # Sample CSV data for testing
│   └── README.md                    # Data files documentation
├── dev/
│   ├── create-test-excel.html       # Development utility for test data creation
│   ├── sample-data-generator.html   # Advanced sample data generator
│   ├── test-alert-system.html       # Alert system testing interface
│   ├── test-validation.html         # Data validation testing
│   └── README.md                    # Development files documentation
└── README.md               # Main project documentation
```

## Sample Data Format

Your Excel file should have the following structure:

| Transaction ID | Trans Ref No | Customer Id | Customer Name | Account No | ... |
|---------------|--------------|-------------|---------------|------------|-----|
| TXN001        | REF001       | CUST001     | John Doe      | ACC001     | ... |
| TXN002        | REF002       | CUST002     | Jane Smith    | ACC002     | ... |

## Troubleshooting

### Common Issues
1. **"Missing required columns" error**: Ensure Excel file has all 39 columns in exact order
2. **"Invalid file format" error**: Use only .xlsx or .xls files
3. **"File too large" error**: Reduce file size to under 50MB
4. **Loading issues**: Ensure stable internet connection for CDN resources

### Performance Tips
- For large files (>10,000 rows), processing may take a few seconds
- Use modern browsers for optimal performance
- Close other browser tabs if experiencing memory issues

## Support
This application runs entirely in your browser and does not collect or transmit any data. All processing is performed locally on your device for maximum security and privacy.

## Production Deployment

### Quick Start
1. Download or clone the project files
2. Open `index.html` in a modern web browser
3. Upload Excel files using the drag-and-drop interface
4. Navigate between tabs to access different features

### Deployment Notes
- **Client-side only**: No server setup required
- **CDN dependencies**: Requires internet connection for external libraries
- **Browser compatibility**: Modern browsers with ES6 support
- **File structure**: Maintain relative paths for proper functionality

## Version
Version 2.0 - Production-Ready AML/CFT Transaction Analysis Dashboard

## Recent Updates
- **Rule Configuration System**: Real-time alert rule customization with immediate application
- **Configurable Alert Parameters**: Flexible threshold, time window, and matching criteria settings
- **Alert Management System**: Comprehensive alert detection and management capabilities
- **High-Value Alert Threshold**: Enhanced detection with configurable minimum amount threshold
- **Flexible Time Windows**: Configurable detection periods from 1-7 days
- **Counter Party Matching**: Optional advanced filtering for counter party requirements
- **Consolidated Alert Approach**: Configurable grouping of alerts by customer and period
- **Enhanced UI**: Three-tab navigation with professional rule configuration interface
- **Export Enhancements**: Export both transaction data and alerts with detailed breakdowns
- **Bulk Operations**: Manage multiple alerts simultaneously
- **Investigation Tools**: Add notes and track alert status changes
- **Multi-Pair Support**: Handle multiple suspicious transaction pairs per customer per period
